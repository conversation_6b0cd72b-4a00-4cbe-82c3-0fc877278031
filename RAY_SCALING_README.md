# Ray-Scaled F5-TTS System

This document describes the Ray-based scaling implementation for F5-TTS that creates 5 instances with optimized resource allocation.

## Architecture

### Resource Allocation
- **5 F5TTSWrapper instances** running as Ray actors
- **0.3 GPU per instance** (1.5 GPU total from CUDA:0)
- **1.2 CPU cores per instance** (6 CPU cores total)
- **Load balancing** with round-robin distribution
- **Fault tolerance** with health monitoring

### Components

1. **F5TTSActor** (`src/f5_tts_api/ray_f5tts_actor.py`)
   - Ray actor wrapper for F5TTSWrapper
   - Resource allocation: 0.3 GPU + 1.2 CPU cores
   - Health monitoring and device info

2. **F5TTSActorPool** (`src/f5_tts_api/ray_f5tts_actor.py`)
   - Manages pool of 5 F5TTS actors
   - Round-robin load balancing
   - Health checks for all actors

3. **Ray Serve Deployment** (`src/f5_tts_api/ray_serve_deployment.py`)
   - Ray Serve deployment managing the actor pool
   - HTTP API endpoints
   - System monitoring

4. **Updated FastAPI** (`main.py`)
   - Integrates with Ray Serve deployment
   - Fallback to direct wrapper if <PERSON> unavailable
   - New monitoring endpoints

## Quick Start

### 1. Start the Ray-scaled system
```bash
python scripts/start_ray_f5tts.py
```

This will:
- Start Ray cluster with required resources
- Deploy F5TTS service with 5 actors
- Start FastAPI server on port 8080
- Start Ray dashboard on port 8265

### 2. Check system status
```bash
python scripts/manage_ray_f5tts.py status
```

### 3. Test the system
```bash
python scripts/manage_ray_f5tts.py test
```

## API Endpoints

### Main API (Port 8080)
- `POST /synthesize/` - Speech synthesis (Ray-scaled)
- `GET /health` - System health check
- `GET /system-info` - Detailed system information
- `GET /` - API information

### Ray Serve API (Port 8000)
- `POST /f5tts/synthesize_speech` - Direct Ray Serve endpoint
- `GET /f5tts/health_check` - Ray service health
- `GET /f5tts/get_system_info` - Ray system info

### Ray Dashboard (Port 8265)
- Web interface for monitoring Ray cluster
- Actor status and resource usage
- Performance metrics

## Management Commands

```bash
# Check status
python scripts/manage_ray_f5tts.py status

# Restart F5TTS service
python scripts/manage_ray_f5tts.py restart

# Stop all services
python scripts/manage_ray_f5tts.py stop

# Run tests
python scripts/manage_ray_f5tts.py test

# View logs info
python scripts/manage_ray_f5tts.py logs
```

## Resource Monitoring

### Check GPU Usage
```bash
nvidia-smi
```

### Check Ray Resources
```bash
ray status  # If Ray CLI is installed
```

### Web Dashboard
Visit http://localhost:8265 for the Ray dashboard

## Configuration

Edit `config/ray_serve_config.yaml` to modify:
- Number of actors
- Resource allocation per actor
- Scaling parameters
- Performance settings

## Troubleshooting

### Common Issues

1. **GPU Memory Issues**
   - Check available GPU memory with `nvidia-smi`
   - Reduce number of actors or GPU allocation per actor

2. **Ray Connection Issues**
   - Ensure Ray cluster is running
   - Check Ray dashboard at http://localhost:8265

3. **Actor Initialization Failures**
   - Check CUDA availability
   - Verify F5TTS model downloads

### Debug Commands

```bash
# Check Ray cluster status
ray status

# List Ray Serve deployments
python -c "import ray; from ray import serve; ray.init(address='auto'); print(serve.list_deployments())"

# Check actor health
python scripts/manage_ray_f5tts.py test
```

## Performance Optimization

### Scaling Configuration
- Increase `max_concurrent_queries` for higher throughput
- Adjust `num_actors` based on available resources
- Monitor GPU memory usage and adjust `gpu_per_actor`

### Load Testing
Use tools like `wrk` or `ab` to test the scaled system:

```bash
# Example load test (install wrk first)
wrk -t4 -c10 -d30s --timeout 60s http://localhost:8080/health
```

## Development

### Adding New Features
1. Modify `F5TTSActor` for new actor capabilities
2. Update `F5TTSServeDeployment` for new endpoints
3. Add corresponding FastAPI routes in `main.py`

### Testing Changes
1. Stop existing services: `python scripts/manage_ray_f5tts.py stop`
2. Start with new code: `python scripts/start_ray_f5tts.py`
3. Run tests: `python scripts/manage_ray_f5tts.py test`

## Production Deployment

For production use:
1. Use dedicated Ray cluster
2. Configure proper logging and monitoring
3. Set up health checks and auto-restart
4. Use load balancer for FastAPI instances
5. Monitor resource usage and scale accordingly
