"""
Ray Actor wrapper for F5TTSWrapper to enable distributed scaling
"""
import ray
import torch
import logging
from typing import Optional, Tuple, Any
from f5_tts_api.f5tts_wrapper import F5TTSWrapper

logger = logging.getLogger(__name__)


@ray.remote(num_cpus=1.2, num_gpus=0.3)
class F5TTSActor:
    """
    Ray actor that wraps F5TTSWrapper for distributed inference
    Each actor uses 1.2 CPU cores and 0.3 GPU (from CUDA:0)
    """
    
    def __init__(self, model_type: str = "F5-TTS_v1", device: str = "cuda:0"):
        """
        Initialize F5TTS actor with specified resources
        
        Args:
            model_type: Model type to use
            device: CUDA device to use (cuda:0)
        """
        self.device = device
        self.model_type = model_type
        
        # Set CUDA device for this actor
        if torch.cuda.is_available():
            torch.cuda.set_device(0)  # Use GPU 0
            
        # Initialize F5TTSWrapper
        self.f5tts = F5TTSWrapper(
            model_type=model_type,
            device=device
        )
        
        logger.info(f"F5TTS Actor initialized with device: {device}, model: {model_type}")
    
    def infer(
        self,
        ref_audio_orig: str,
        ref_text: Optional[str],
        gen_text: str,
        model_type: Optional[str] = None,
        remove_silence: bool = False,
        seed: int = -1,
        cross_fade_duration: float = 0.15,
        nfe_step: int = 32,
        speed: float = 1.0,
        show_info: Any = None
    ) -> Tuple[Tuple[int, Any], Optional[str], str, int]:
        """
        Perform TTS inference using the wrapped F5TTSWrapper
        
        Returns:
            Tuple of (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed
        """
        try:
            result = self.f5tts.infer(
                ref_audio_orig=ref_audio_orig,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type or self.model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=show_info or logger.info
            )
            return result
        except Exception as e:
            logger.error(f"Error in F5TTS inference: {str(e)}")
            raise
    
    def get_device_info(self) -> dict:
        """Get information about the device this actor is using"""
        return {
            "device": self.device,
            "model_type": self.model_type,
            "cuda_available": torch.cuda.is_available(),
            "current_device": torch.cuda.current_device() if torch.cuda.is_available() else None,
            "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
        }
    
    def health_check(self) -> bool:
        """Simple health check for the actor"""
        try:
            return self.f5tts is not None and torch.cuda.is_available()
        except Exception:
            return False


class F5TTSActorPool:
    """
    Pool manager for F5TTS actors with load balancing
    """
    
    def __init__(self, num_actors: int = 5, model_type: str = "F5-TTS_v1"):
        """
        Initialize pool of F5TTS actors
        
        Args:
            num_actors: Number of actor instances (default: 5)
            model_type: Model type for all actors
        """
        self.num_actors = num_actors
        self.model_type = model_type
        self.actors = []
        self.current_actor_idx = 0
        
        # Initialize Ray if not already initialized
        if not ray.is_initialized():
            ray.init()
        
        # Create actor pool
        self._create_actors()
        
        logger.info(f"F5TTS Actor Pool initialized with {num_actors} actors")
    
    def _create_actors(self):
        """Create the pool of F5TTS actors"""
        self.actors = []
        for i in range(self.num_actors):
            actor = F5TTSActor.remote(model_type=self.model_type, device="cuda:0")
            self.actors.append(actor)
            logger.info(f"Created F5TTS actor {i+1}/{self.num_actors}")
    
    def get_next_actor(self) -> Any:
        """Get the next available actor using round-robin load balancing"""
        actor = self.actors[self.current_actor_idx]
        self.current_actor_idx = (self.current_actor_idx + 1) % self.num_actors
        return actor
    
    async def infer(self, *args, **kwargs):
        """
        Perform inference using the next available actor
        """
        actor = self.get_next_actor()
        return await actor.infer.remote(*args, **kwargs)
    
    async def health_check_all(self) -> dict:
        """Check health of all actors"""
        health_futures = [actor.health_check.remote() for actor in self.actors]
        health_results = await ray.get(health_futures)
        
        return {
            "total_actors": self.num_actors,
            "healthy_actors": sum(health_results),
            "actor_health": health_results
        }
    
    async def get_device_info_all(self) -> list:
        """Get device info from all actors"""
        info_futures = [actor.get_device_info.remote() for actor in self.actors]
        return await ray.get(info_futures)
    
    def shutdown(self):
        """Shutdown all actors and Ray"""
        for actor in self.actors:
            ray.kill(actor)
        self.actors = []
        logger.info("F5TTS Actor Pool shutdown complete")
