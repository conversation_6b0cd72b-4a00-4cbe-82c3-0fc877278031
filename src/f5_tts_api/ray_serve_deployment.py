"""
Ray Serve deployment for F5-TTS with 5 scaled instances
Each instance uses 0.3 GPU and 1.2 CPU cores from CUDA:0
"""
import ray
from ray import serve
import asyncio
import logging
from typing import Optional, Dict, Any
from fastapi import FastAPI, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
import shutil
import os

from f5_tts_api.ray_f5tts_actor import F5TTSActorPool

logger = logging.getLogger(__name__)


@serve.deployment(
    num_replicas=1,  # Single deployment managing the actor pool
    ray_actor_options={
        "num_cpus": 1,  # CPU for the deployment coordinator
        "num_gpus": 0   # No GPU for coordinator, actors handle GPU
    },
    max_ongoing_requests=5,  # Allow multiple concurrent requests
    health_check_period_s=30,
    health_check_timeout_s=10
)
class F5TTSServeDeployment:
    """
    Ray Serve deployment that manages a pool of F5TTS actors
    """
    
    def __init__(self):
        """Initialize the F5TTS service with actor pool"""
        self.actor_pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize the F5TTS actor pool"""
        try:
            self.actor_pool = F5TTSActorPool(
                num_actors=5,  # 5 instances as requested
                model_type="F5-TTS_v1"
            )
            logger.info("F5TTS Actor Pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize F5TTS Actor Pool: {str(e)}")
            raise
    
    async def synthesize_speech(
        self,
        ref_audio_path: str,
        ref_text: Optional[str] = None,
        gen_text: str = "",
        model_type: Optional[str] = "F5-TTS_v1",
        remove_silence: Optional[bool] = False,
        seed: Optional[int] = -1,
        cross_fade_duration: Optional[float] = 0.15,
        nfe_step: Optional[int] = 32,
        speed: Optional[float] = 1.0,
    ) -> Dict[str, Any]:
        """
        Synthesize speech using the F5TTS actor pool
        
        Args:
            ref_audio_path: Path to reference audio file
            ref_text: Reference text (if empty, will be transcribed)
            gen_text: Text to generate
            model_type: Model type to use
            remove_silence: Whether to remove silence from output
            seed: Random seed (-1 for random)
            cross_fade_duration: Cross-fade duration between segments
            nfe_step: Number of denoising steps
            speed: Speed multiplier
            
        Returns:
            Dictionary with synthesis results
        """
        if not self.actor_pool:
            raise HTTPException(status_code=500, detail="F5TTS Actor Pool not initialized")
        
        try:
            logger.info(f"Starting synthesis with gen_text: {gen_text[:50]}...")
            
            # Perform inference using actor pool
            result = await self.actor_pool.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed
            )
            
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = result
            
            logger.info(f"Synthesis successful. Sample rate: {sample_rate}, Used seed: {used_seed}")
            
            return {
                "sample_rate": sample_rate,
                "audio_data": audio_data,
                "spectrogram_path": spectrogram_path,
                "processed_ref_text": processed_ref_text,
                "used_seed": used_seed
            }
            
        except Exception as e:
            logger.error(f"Error during speech synthesis: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check endpoint"""
        if not self.actor_pool:
            return {"status": "unhealthy", "reason": "Actor pool not initialized"}
        
        try:
            health_info = await self.actor_pool.health_check_all()
            return {
                "status": "healthy" if health_info["healthy_actors"] > 0 else "unhealthy",
                "actor_pool_info": health_info
            }
        except Exception as e:
            return {"status": "unhealthy", "reason": str(e)}
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information from all actors"""
        if not self.actor_pool:
            return {"error": "Actor pool not initialized"}
        
        try:
            device_info = await self.actor_pool.get_device_info_all()
            health_info = await self.actor_pool.health_check_all()
            
            return {
                "num_actors": self.actor_pool.num_actors,
                "model_type": self.actor_pool.model_type,
                "device_info": device_info,
                "health_info": health_info,
                "resource_allocation": {
                    "gpu_per_actor": 0.3,
                    "cpu_per_actor": 1.2,
                    "total_gpu_used": 0.3 * 5,  # 1.5 GPU total
                    "total_cpu_used": 1.2 * 5   # 6 CPU total
                }
            }
        except Exception as e:
            return {"error": str(e)}


def create_ray_serve_app():
    """Create and configure the Ray Serve application"""
    
    # Initialize Ray if not already done
    if not ray.is_initialized():
        ray.init(
            num_cpus=8,  # Ensure enough CPUs for the deployment
            num_gpus=2   # Ensure enough GPUs (we'll use 1.5 total)
        )
    
    # Start Ray Serve
    serve.start(detached=True, http_options={"host": "0.0.0.0", "port": 8000})
    
    # Deploy the F5TTS service
    F5TTSServeDeployment.deploy(name="f5tts-service", route_prefix="/f5tts")
    
    logger.info("Ray Serve F5TTS deployment started successfully")
    logger.info("Service available at: http://0.0.0.0:8000/f5tts")
    
    return serve.get_deployment("f5tts-service")


async def test_deployment():
    """Test the deployment"""
    deployment = serve.get_deployment("f5tts-service")
    handle = deployment.get_handle()
    
    # Test health check
    health = await handle.health_check.remote()
    print(f"Health check: {health}")
    
    # Test system info
    info = await handle.get_system_info.remote()
    print(f"System info: {info}")


if __name__ == "__main__":
    import asyncio
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    # Create and start the deployment
    deployment = create_ray_serve_app()
    
    # Run test
    asyncio.run(test_deployment())
    
    print("Ray Serve deployment is running. Press Ctrl+C to stop.")
    
    try:
        # Keep the script running
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down Ray Serve...")
        serve.shutdown()
        ray.shutdown()
