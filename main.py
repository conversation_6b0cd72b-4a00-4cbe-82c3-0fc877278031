import logging
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, Request, HTTPException
from fastapi.responses import FileResponse
import tempfile
import soundfile as sf
from typing import Optional
import shutil
import os
import ray
from ray import serve
import asyncio

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(title="F5-TTS Web API - Ray Scaled")

# Ray Serve deployment handle (will be initialized)
f5tts_handle = None

def initialize_ray_client():
    """Initialize Ray client and get deployment handle"""
    global f5tts_handle
    try:
        # Connect to Ray Serve deployment
        if not ray.is_initialized():
            ray.init(address="auto")  # Connect to existing Ray cluster

        # Get handle to the F5TTS service
        f5tts_handle = serve.get_deployment("f5tts-service").get_handle()
        logger.info("Connected to Ray Serve F5TTS deployment")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Ray Serve: {str(e)}")
        return False

# Initialize Ray connection on startup
@app.on_event("startup")
async def startup_event():
    """Initialize Ray connection on FastAPI startup"""
    success = initialize_ray_client()
    if not success:
        logger.warning("Ray Serve connection failed - falling back to direct F5TTSWrapper")
        # Fallback to direct wrapper if Ray is not available
        global tts
        from f5_tts_api.f5tts_wrapper import F5TTSWrapper
        tts = F5TTSWrapper(model_type="F5-TTS_v1")

@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info("Incoming request: %s %s", request.method, request.url)
    response = await call_next(request)
    logger.info("Completed request with status code: %d", response.status_code)
    return response

@app.post("/synthesize/")
async def synthesize_speech(
    ref_audio: UploadFile,
    ref_text: Optional[str] = Form(None),
    gen_text: str = Form(...),
    model_type: Optional[str] = Form("F5-TTS_v1"),
    remove_silence: Optional[bool] = Form(False),
    seed: Optional[int] = Form(-1),
    cross_fade_duration: Optional[float] = Form(0.15),
    nfe_step: Optional[int] = Form(32),
    speed: Optional[float] = Form(1.0),
):
    """
    Synthesize speech using F5-TTS with enhanced parameters following infer_gradio structure

    Args:
        ref_audio: Reference audio file
        ref_text: Reference text (if empty, will be transcribed)
        gen_text: Text to generate
        model_type: Model type to use ("F5-TTS_v1" or "E2-TTS")
        remove_silence: Whether to remove silence from output
        seed: Random seed (-1 for random)
        cross_fade_duration: Cross-fade duration between segments
        nfe_step: Number of denoising steps
        speed: Speed multiplier
    """
    logger.info("Endpoint '/synthesize/' called.")
    logger.info("Received file: %s", ref_audio.filename)
    logger.info("ref_text: %s", ref_text)
    logger.info("gen_text preview: %s", gen_text[:50])
    logger.info("Parameters - model_type: %s, remove_silence: %s, seed: %s, cross_fade_duration: %s, nfe_step: %s, speed: %s",
                model_type, remove_silence, seed, cross_fade_duration, nfe_step, speed)

    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio.file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            logger.info("Saved reference audio to: %s", tmp_audio_path)

        # Use Ray Serve if available, otherwise fallback to direct wrapper
        if f5tts_handle:
            # Use Ray Serve deployment
            result = await f5tts_handle.synthesize_speech.remote(
                ref_audio_path=tmp_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed
            )
            sample_rate = result["sample_rate"]
            audio_data = result["audio_data"]
            spectrogram_path = result["spectrogram_path"]
            processed_ref_text = result["processed_ref_text"]
            used_seed = result["used_seed"]
        else:
            # Fallback to direct wrapper
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts.infer(
                ref_audio_orig=tmp_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info,
            )

        logger.info("Synthesis successful. Sample rate: %d, Used seed: %s", sample_rate, used_seed)
        logger.info("Processed ref_text: %s", processed_ref_text)
        if spectrogram_path:
            logger.info("Spectrogram saved to: %s", spectrogram_path)

        # Save output audio
        out_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(out_path, audio_data, sample_rate)
        logger.info("Generated speech saved to: %s", out_path)

        # Clean up temporary reference audio file
        try:
            os.unlink(tmp_audio_path)
        except OSError:
            logger.warning("Could not delete temporary file: %s", tmp_audio_path)

        return FileResponse(out_path, media_type="audio/wav", filename="output.wav")

    except Exception as e:
        logger.exception("Error during speech synthesis: %s", str(e))
        # Clean up temporary files on error
        try:
            if 'tmp_audio_path' in locals():
                os.unlink(tmp_audio_path)
        except OSError:
            pass
        raise HTTPException(status_code=500, detail=f"Speech synthesis failed: {str(e)}")

@app.get("/")
def root():
    logger.info("Endpoint '/' called.")
    return {
        "message": "Welcome to the F5-TTS Web API - Ray Scaled",
        "version": "3.0",
        "scaling": {
            "backend": "Ray Serve",
            "instances": 5,
            "gpu_per_instance": 0.3,
            "cpu_per_instance": 1.2,
            "total_gpu_used": 1.5,
            "total_cpu_used": 6.0
        },
        "features": [
            "Ray-based horizontal scaling with 5 instances",
            "GPU resource sharing (0.3 GPU per instance)",
            "Load balancing across instances",
            "Enhanced synthesis with infer_gradio structure",
            "Multiple model support (F5-TTS_v1, E2-TTS)",
            "Advanced parameters (speed, cross_fade_duration, nfe_step)",
            "Silence removal",
            "Spectrogram generation",
            "Health monitoring and system info"
        ],
        "endpoints": {
            "/synthesize/": "Enhanced synthesis with Ray scaling",
            "/health": "Ray system health check",
            "/system-info": "Ray system information",
            "/": "API information"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for Ray system"""
    logger.info("Health check endpoint called")

    if f5tts_handle:
        try:
            health_info = await f5tts_handle.health_check.remote()
            return {
                "status": "healthy",
                "backend": "ray_serve",
                "ray_health": health_info
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "ray_serve",
                "error": str(e)
            }
    else:
        return {
            "status": "healthy",
            "backend": "direct_wrapper",
            "message": "Using direct F5TTSWrapper (Ray not available)"
        }

@app.get("/system-info")
async def system_info():
    """Get detailed system information"""
    logger.info("System info endpoint called")

    if f5tts_handle:
        try:
            info = await f5tts_handle.get_system_info.remote()
            return {
                "backend": "ray_serve",
                "system_info": info
            }
        except Exception as e:
            return {
                "backend": "ray_serve",
                "error": str(e)
            }
    else:
        return {
            "backend": "direct_wrapper",
            "message": "Using direct F5TTSWrapper (Ray not available)",
            "fallback_info": {
                "instances": 1,
                "model_type": "F5-TTS_v1"
            }
        }
