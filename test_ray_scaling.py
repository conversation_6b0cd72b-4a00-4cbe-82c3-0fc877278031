#!/usr/bin/env python3
"""
Test script for Ray-scaled F5-TTS system
"""
import sys
import time
import asyncio
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import ray
from ray import serve

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_ray_scaling():
    """Test the Ray scaling functionality"""
    logger.info("Testing Ray-scaled F5-TTS system...")
    
    try:
        # Connect to <PERSON>
        if not ray.is_initialized():
            ray.init(address="auto")
        
        # Get deployment handle
        deployment = serve.get_deployment("f5tts-service")
        handle = deployment.get_handle()
        
        # Test 1: Health check
        logger.info("Test 1: Health check")
        health = await handle.health_check.remote()
        logger.info(f"Health check result: {health}")
        
        # Test 2: System info
        logger.info("Test 2: System information")
        info = await handle.get_system_info.remote()
        logger.info(f"System info: {info}")
        
        # Test 3: Verify resource allocation
        logger.info("Test 3: Resource allocation verification")
        if "resource_allocation" in info:
            allocation = info["resource_allocation"]
            logger.info(f"GPU per actor: {allocation.get('gpu_per_actor')}")
            logger.info(f"CPU per actor: {allocation.get('cpu_per_actor')}")
            logger.info(f"Total GPU used: {allocation.get('total_gpu_used')}")
            logger.info(f"Total CPU used: {allocation.get('total_cpu_used')}")
            
            # Verify expected values
            assert allocation.get('gpu_per_actor') == 0.3, "GPU per actor should be 0.3"
            assert allocation.get('cpu_per_actor') == 1.2, "CPU per actor should be 1.2"
            assert allocation.get('total_gpu_used') == 1.5, "Total GPU should be 1.5"
            assert allocation.get('total_cpu_used') == 6.0, "Total CPU should be 6.0"
            logger.info("✓ Resource allocation verified")
        
        # Test 4: Actor count
        logger.info("Test 4: Actor count verification")
        if "num_actors" in info:
            num_actors = info["num_actors"]
            assert num_actors == 5, f"Expected 5 actors, got {num_actors}"
            logger.info(f"✓ Actor count verified: {num_actors}")
        
        # Test 5: Device info
        logger.info("Test 5: Device information")
        if "device_info" in info:
            device_info = info["device_info"]
            logger.info(f"Number of device info entries: {len(device_info)}")
            
            for i, device in enumerate(device_info):
                logger.info(f"Actor {i+1}: {device}")
                assert device.get('device') == 'cuda:0', f"Actor {i+1} should use cuda:0"
            
            logger.info("✓ Device information verified")
        
        # Test 6: Health of all actors
        logger.info("Test 6: Actor health verification")
        if "health_info" in info:
            health_info = info["health_info"]
            healthy_actors = health_info.get("healthy_actors", 0)
            total_actors = health_info.get("total_actors", 0)
            
            logger.info(f"Healthy actors: {healthy_actors}/{total_actors}")
            assert healthy_actors == 5, f"Expected 5 healthy actors, got {healthy_actors}"
            logger.info("✓ All actors are healthy")
        
        logger.info("=" * 60)
        logger.info("✓ All tests passed! Ray scaling is working correctly.")
        logger.info("=" * 60)
        logger.info("Configuration verified:")
        logger.info("  - 5 F5TTS actor instances")
        logger.info("  - 0.3 GPU per instance (1.5 GPU total)")
        logger.info("  - 1.2 CPU cores per instance (6 CPU total)")
        logger.info("  - All actors using CUDA:0")
        logger.info("  - All actors healthy and ready")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False


async def test_concurrent_requests():
    """Test concurrent request handling"""
    logger.info("Testing concurrent request handling...")
    
    try:
        deployment = serve.get_deployment("f5tts-service")
        handle = deployment.get_handle()
        
        # Create multiple concurrent health check requests
        tasks = []
        num_requests = 10
        
        logger.info(f"Sending {num_requests} concurrent requests...")
        start_time = time.time()
        
        for i in range(num_requests):
            task = handle.health_check.remote()
            tasks.append(task)
        
        # Wait for all requests to complete
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Completed {num_requests} requests in {duration:.2f} seconds")
        logger.info(f"Average response time: {duration/num_requests:.3f} seconds")
        
        # Verify all requests succeeded
        successful = sum(1 for result in results if result.get("status") == "healthy")
        logger.info(f"Successful requests: {successful}/{num_requests}")
        
        assert successful == num_requests, f"Expected {num_requests} successful requests"
        logger.info("✓ Concurrent request test passed")
        
        return True
        
    except Exception as e:
        logger.error(f"Concurrent request test failed: {str(e)}")
        return False


def main():
    """Main test function"""
    logger.info("Starting Ray-scaled F5-TTS tests...")
    
    async def run_tests():
        # Test 1: Basic functionality
        test1_success = await test_ray_scaling()
        
        # Test 2: Concurrent requests
        test2_success = await test_concurrent_requests()
        
        return test1_success and test2_success
    
    # Run tests
    success = asyncio.run(run_tests())
    
    if success:
        logger.info("🎉 All tests passed! Ray scaling is working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
