version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=user
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - "5672:5672"      # RabbitMQ client port
      - "15672:15672"    # RabbitMQ management UI

  tts-api:
    build:
      context: .
      dockerfile: Dockerfile.tts
    runtime: nvidia
    environment:
      - INSTANCE_ROLE=api
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=user
      - RABBITMQ_PASS=password
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    ports:
      - "8000:8000"
    volumes:
      - ~/.cache/huggingface:/root/.cache/huggingface
      - ~/.cache/pip:/root/.cache/pip
    depends_on:
      - rabbitmq

  tts-worker:
    build:
      context: .
      dockerfile: Dockerfile.tts
    runtime: nvidia
    environment:
      - INSTANCE_ROLE=worker
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=user
      - RABBITMQ_PASS=password
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ~/.cache/huggingface:/root/.cache/huggingface
      - ~/.cache/pip:/root/.cache/pip
    depends_on:
      - rabbitmq

